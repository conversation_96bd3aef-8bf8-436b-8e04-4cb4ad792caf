<template>
  <el-card class="box-card">
    <div slot="header" class="header-box">
      <div>通行证线路规划</div>
    </div>
    <div class="cards-container">
      <div style="position: relative">
        <BMapComp :config="mapConfig" @mapReady="mapReady"></BMapComp>
        <div class="contract-list-box">
          <!-- 显示全部线路按钮 -->
          <div class="all-routes-btn" :class="{ active: showAllRoutes }" @click="toggleShowAllRoutes">显示全部线路</div>

          <!-- 合同列表 -->
          <div class="contract-item" :class="isSelect(item)" v-for="(item, index) in contractList" :key="item.id" @click="selectContract(item)">
            <span>合同-{{ index + 1 }}</span>
          </div>
        </div>
        <div class="route-list-box">
          <div class="search-box">
            <el-input placeholder="搜索路线" v-model="searchText" clearable prefix-icon="el-icon-search"></el-input>
          </div>
          <div class="route-list">
            <div class="route-item" :class="{ selected: isRouteSelected(item.rteLinePk) }" v-for="item in filteredRouteList" :key="item.rteLinePk" @click="handleRouteItemClick(item)">
              <div class="route-content">
                <div class="route-name text-ellipsis" :title="item.label">
                  {{ item.label || "未命名路线" }}
                </div>

                <!-- 操作按钮：仅在合同模式下显示 -->
                <div v-if="!isView && !showAllRoutes && selectContractId" class="route-actions">
                  <i style="color: #67c23a" v-if="!isRouteInCurrentContract(item.rteLinePk)" class="el-icon-circle-plus-outline" @click.stop="submitAddRoute(item)"></i>
                  <i v-else class="el-icon-delete" style="color: #f56c6c" @click.stop="confirmRemoveRoute(item)"></i>
                  <!-- <el-button v-if="!isRouteInCurrentContract(item.rteLinePk)" size="mini" type="primary" @click.stop="submitAddRoute(item)">添加</el-button>
                  <el-button v-else size="mini" type="danger" @click.stop="confirmRemoveRoute(item)">删除</el-button> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <el-table
          class="el-table"
          :data="routeDataList"
          border
          style="width: 100%"
          v-loading="listLoading"
          height="32vh"
          size="small"
          row-key="key"
          :tree-props="{ children: 'children' }"
          :default-expand-all="false"
        >
          <el-table-column label="合同ID" prop="contractId" min-width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.contractName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="路线名称" prop="routeName" min-width="200">
            <template slot-scope="scope">
              <span v-if="scope.row.isContract">包含{{ scope.row.routeCount }}条路线</span>
              <span v-else>{{ scope.row.routeName || "未命名路线" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时段禁行" prop="denyType" min-width="120">
            <template slot-scope="scope">
              <el-select v-if="!scope.row.isContract" size="small" disabled v-model="scope.row.denyType" placeholder="请选择">
                <el-option v-for="item in denyTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="限制通行" prop="notWeight" min-width="120">
            <template slot-scope="scope">
              <el-select v-if="!scope.row.isContract" size="small" disabled v-model="scope.row.notWeight" placeholder="请选择">
                <el-option v-for="item in notWeightOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100" align="center">
            <template slot-scope="scope">
              <el-button v-if="scope.row.isContract" type="text" title="查看" @click="viewContract(scope.row)">查看</el-button>
              <el-button v-else-if="!isView && !scope.row.isContract" type="text" title="删除" @click="submitRemoveRoute(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog width="45%" :title="currentViewContract ? `${currentViewContract.contractName} - 路线列表` : '路线列表'" :visible.sync="innerVisible" append-to-body>
      <div v-if="currentViewRoutes.length > 0">
        <div class="route-tags-container">
          <el-tag v-for="route in currentViewRoutes" :key="route.routeId" class="route-tag">
            {{ route.routeName || "未命名路线" }}
          </el-tag>
        </div>
      </div>
      <div v-else style="text-align: center; padding: 20px; color: #999">该合同暂无路线</div>
    </el-dialog>

    <!-- 企业查询弹窗 -->
    <el-dialog width="35%" title="查询默认路线" :visible.sync="enterpriseQueryVisible" append-to-body :close-on-click-modal="false" class="enterprise-query-dialog">
      <div class="dialog-content">
        <!-- 第一步：企业简称查询 -->
        <div class="query-section">
          <p class="main-title">该合同暂无路线，是否需要查询镇海的企业默认路线？</p>
          <p class="sub-title">请输入在镇海的企业简称进行查询并选择（可多选）：</p>
          <div>
            <span>企业简称：</span>
            <el-select
              v-model="selectedEnterprises"
              size="small"
              multiple
              filterable
              remote
              reserve-keyword
              placeholder="请输入企业简称进行查询"
              :remote-method="remoteSearchEnterprises"
              :loading="searchLoading"
              value-key="id"
              class="enterprise-select"
              @change="generateRoutePreview"
            >
              <el-option v-for="option in enterpriseOptions" :key="option.id" :label="option.tagName" :value="option"></el-option>
            </el-select>
          </div>
        </div>
        <!-- 路线预览（可折叠） -->
        <div v-if="selectedEnterprises.length > 0" class="route-preview-section">
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="预览路线" name="routePreview">
              <div v-if="previewRoutes.length > 0">
                <p class="preview-description">根据您选择的企业，找到以下路线：</p>

                <div class="route-tags-container">
                  <el-tag v-for="route in previewRoutes" :key="route.routeId" type="info" class="route-tag">
                    {{ route.routeName || "未命名路线" }}
                  </el-tag>
                </div>
              </div>
              <div v-else class="no-routes-tip">未找到相关路线</div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="enterpriseQueryVisible = false">取消</el-button>
        <el-button v-if="previewRoutes.length > 0" type="primary" @click="batchAddRoutes" :loading="listLoading" :disabled="previewRoutes.length === 0">一键添加</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import BMapComp from "./BMapComp";
import * as $http from "@/api/roadPass";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
export default {
  name: "",
  components: {
    BMapComp,
  },
  props: {
    contractList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    templateId: {
      type: String,
      default: false, // 默认
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      map: null,
      mapConfig: {
        scrollWheelZoom: true,
        mapHeight: 400,
        centerAndZoom: {
          point: [121.681275, 29.973008],
          zoom: 13,
          city: "宁波市",
        },
      },

      allRouteList: [],
      searchText: "", // 搜索文本
      // time: null,

      selectContractId: "", // 当前选中的合同ID（单选）
      showAllRoutes: false, // 显示全部线路模式
      routeMap: {}, // 存储路线数据
      selectedRouteId: "", // 当前选中的路线ID
      contractSelectRouteMap: {}, // 存储合同列已添加的路线ID
      infoBox: null, // 信息窗口

      listLoading: false,
      routeDataList: [], // 合同已选择的线路数据列表
      innerVisible: false,
      currentViewContract: null, // 当前查看的合同信息
      currentViewRoutes: [], // 当前查看合同的路线列表

      // 企业查询弹窗相关
      enterpriseQueryVisible: false, // 企业查询弹窗显示状态
      currentQueryContract: null, // 当前查询的合同信息
      enterpriseOptions: [], // 后端返回的企业选项
      selectedEnterprises: [], // 用户选择的企业
      previewRoutes: [], // 预览的路线列表
      showRoutePreview: false, // 是否显示路线预览
      searchLoading: false, // 远程搜索loading状态
      searchTimer: null, // 搜索防抖定时器
      activeCollapse: [], // 折叠面板激活项
      // morning 早高峰禁行,  evening 晚高峰禁行, both 高峰禁行, none 全时段通行， 默认写both
      denyTypeOptions: [
        { label: "高峰禁行", value: "both" },
        { label: "早高峰禁行", value: "morning" },
        { label: "晚高峰禁行", value: "evening" },
        { label: "全时段通行", value: "none" },
      ],
      notWeightOptions: [
        { label: "不限空重车", value: false },
        { label: "仅空车", value: true },
      ],
    };
  },
  computed: {
    // 获取所有合同使用的路线（去重）
    contractUsedRoutes() {
      const usedRouteIds = new Set();
      Object.values(this.contractSelectRouteMap).forEach(routes => {
        routes.forEach(route => usedRouteIds.add(String(route.routeId)));
      });
      return Array.from(usedRouteIds);
    },

    // 当前选中合同的路线
    currentContractRoutes() {
      if (this.selectContractId) {
        const routes = this.contractSelectRouteMap[this.selectContractId] || [];
        return routes.map(route => String(route.routeId));
      }
      return [];
    },

    // 过滤和排序后的路线列表
    filteredRouteList() {
      // 1. 基础搜索过滤
      let filtered = this.allRouteList.filter(item => {
        return !this.searchText || (item.label && item.label.toLowerCase().includes(this.searchText.toLowerCase()));
      });

      // 2. 排序逻辑
      const sorted = filtered.sort((a, b) => {
        if (this.showAllRoutes) {
          // 全部线路模式：合同使用的路线置顶
          const aUsed = this.contractUsedRoutes.includes(String(a.rteLinePk));
          const bUsed = this.contractUsedRoutes.includes(String(b.rteLinePk));
          if (aUsed && !bUsed) return -1;
          if (!aUsed && bUsed) return 1;
        } else if (this.selectContractId) {
          // 合同模式：当前合同路线置顶
          const aInContract = this.currentContractRoutes.includes(String(a.rteLinePk));
          const bInContract = this.currentContractRoutes.includes(String(b.rteLinePk));

          if (aInContract && !bInContract) return -1;
          if (!aInContract && bInContract) return 1;
        }
        return 0;
      });

      return sorted;
    },
  },
  watch: {
    templateId: {
      handler(newVal) {
        if (newVal) {
          this.reset();
        }
      },
      immediate: true,
    },
    contractList: {
      handler(newVal, oldVal) {
        this.handleContractChanges(newVal, oldVal);
      },
      deep: true,
      immediate: false,
    },
  },
  mounted() {},
  methods: {
    // 处理合同列表变化
    handleContractChanges(newContracts, oldContracts) {
      if (!oldContracts || !newContracts) return;

      // 检测新增的合同
      /* const addedContracts = newContracts.filter(newContract => !oldContracts.find(oldContract => oldContract.id === newContract.id)); */

      // 检测删除的合同
      const deletedContracts = oldContracts.filter(oldContract => !newContracts.find(newContract => newContract.id === oldContract.id));

      // 处理新增合同的默认路线
      /* addedContracts.forEach(contract => {
        this.handleNewContract(contract);
      }); */

      // 处理删除合同的路线清理
      deletedContracts.forEach(contract => {
        this.handleDeletedContract(contract);
      });
    },

    // 处理删除合同
    handleDeletedContract(contract) {
      // 清理该合同的所有路线数据（清理前端页面数据）
      if (this.contractSelectRouteMap[contract.id]) {
        delete this.contractSelectRouteMap[contract.id];
        this.updateRouteDataList(); // 更新合同已选择的线路数据列表
      }

      // 如果当前选中的是被删除的合同，进行完整清理
      if (this.selectContractId === contract.id) {
        // 清空合同选择
        this.selectContractId = "";

        // 清空路线选择
        this.removeSelectedRoute(this.selectedRouteId);
        this.selectedRouteId = "";

        // 清理地图显示
        this.clearCurrentMapDisplay();

        // 关闭信息窗口
        if (this.infoBox) {
          this.infoBox.close();
        }
      }

      // 如果在"显示全部线路"模式下，需要重新刷新显示
      if (this.showAllRoutes) {
        // 清理当前显示
        this.clearCurrentMapDisplay();

        // 重新显示剩余合同的路线
        this.showAllContractRoutes();
      }
    },

    // 选择|取消选择合同
    selectContract(data) {
      if (this.showAllRoutes) {
        // 退出全部线路模式
        this.showAllRoutes = false;
        // 清理当前地图显示
        this.clearAllRoutesDisplay();
      }

      if (this.selectContractId === data.id) {
        // 取消选择当前合同
        this.selectContractId = "";
        // 重置合同路线颜色
        this.resetRouteColor(data);
      } else {
        // 选择新合同（单选）
        if (this.selectContractId) {
          // 先重置之前选中的合同
          const prevContract = this.contractList.find(c => c.id === this.selectContractId);
          if (prevContract) {
            // 重置合同路线颜色
            this.resetRouteColor(prevContract);
          }
        }
        this.selectContractId = data.id; // 设置选择的合同ID

        // 检查该合同是否已有路线
        const contractRoutes = this.contractSelectRouteMap[data.id];
        const hasRoutes = contractRoutes && contractRoutes.length > 0;

        if (hasRoutes) {
          // 显示合同已添加路线(合同路线列表)
          this.showDefaultRoute(contractRoutes);
        } else {
          // 新需求：合同无路线时，弹出企业查询弹窗
          this.openEnterpriseQuery(data);
        }
      }

      if (this.infoBox) {
        this.infoBox.close();
      }
    },
    // 显示合同已添加路线
    showDefaultRoute(data) {
      if (data && data.length > 0) {
        let allPathPoints = []; // 收集所有路线的路径点

        data.forEach(item => {
          let polylineArr = this.routeMap[item.routeId];
          if (polylineArr) {
            polylineArr.forEach(polyline => {
              polyline.show();
              polyline.setStrokeColor("#2979ff");

              // 收集路径点
              let path = polyline.getPath();
              allPathPoints.push(...path);
            });
          }
        });

        // 统一设置视口以显示所有路线
        // if (allPathPoints.length > 0) {
        //   this.map.setViewport(allPathPoints);
        // }
      }
    },
    // 重置合同已添加路线
    resetRouteColor(data) {
      // 使用保存的路线ID直接重置颜色，避免API调用
      const routeIds = this.contractSelectRouteMap[data.id] || [];
      routeIds.map(item => {
        let polylineArr = this.routeMap[item.routeId];
        if (polylineArr) {
          polylineArr.map(polyline => {
            polyline.hide();
            polyline.setStrokeColor("#cccccc");
          });
        }
      });
    },

    // 选择路线
    selectRoute(route) {
      // 取消先前选择的路线
      this.removeSelectedRoute(this.selectedRouteId);
      const routeId = route.rteLinePk;
      this.selectedRouteId = routeId;
    },
    // 添加选中路线
    addSelectedRoute(routeId, setViewport = false) {
      const polylineArr = this.routeMap[routeId];
      if (polylineArr) {
        let pathArr = [];

        // 直接显示所有相关的 polyline，不触发 click 事件
        polylineArr.forEach(polyline => {
          polyline.show();
          polyline.setStrokeColor("#ff4081"); // 选中路线颜色

          // 收集所有路径点用于设置地图视口
          let path = polyline.getPath();
          pathArr.push(...path);
        });

        // 设置地图视口以显示所有路线（单独选择路线时需要设置视口）
        if (setViewport && pathArr.length > 0) {
          this.map.setViewport(pathArr);
        }

        // 显示第一条路线的信息窗口（如果有多条路线，显示第一条的中点）
        if (polylineArr.length > 0) {
          const firstPolyline = polylineArr[0];
          const path = firstPolyline.getPath();
          const midIndex = Math.floor(path.length / 2);
          const midPoint = path[midIndex];

          this.showRouteInfoBox(firstPolyline.routeData, midPoint);
        }
      }
    },
    // 移除选中路线
    removeSelectedRoute(routeId) {
      if (routeId) {
        // 重置所有相关 polyline 的颜色和显示状态
        const polylineArr = this.routeMap[routeId];
        if (polylineArr) {
          // 检查当前是否选中了合同，以及该路线是否属于当前选中的合同
          const isInCurrentContract = this.selectContractId && this.isRouteInCurrentContract(routeId);
          const isInAllRoutesMode = this.showAllRoutes && this.contractUsedRoutes.includes(String(routeId));

          polylineArr.forEach(polyline => {
            if (!this.isView) {
              if (isInCurrentContract) {
                // 如果路线属于当前选中的合同，恢复为合同路线状态（蓝色显示）
                polyline.show();
                polyline.setStrokeColor("#2979ff");
              } else if (isInAllRoutesMode) {
                // 如果在全部线路模式下且路线被合同使用，恢复为合同路线状态（蓝色显示）
                polyline.show();
                polyline.setStrokeColor("#2979ff");
              } else {
                // 否则隐藏路线
                polyline.hide();
                polyline.setStrokeColor("#cccccc");
              }
            } else {
              // 查看模式下保持蓝色显示
              polyline.setStrokeColor("#2979ff");
            }
          });
        }

        // 关闭信息窗口
        if (this.infoBox) {
          this.infoBox.close();
        }
      }
    },
    // 地图加载完成
    mapReady(map) {
      this.map = map;
      this.getAllRoute();
    },
    // 切换到全部线路模式
    toggleShowAllRoutes() {
      this.showAllRoutes = true;
      this.selectContractId = ""; // 切换到全部线路模式时取消合同选择
      this.selectedRouteId = ""; // 切换到全部线路模式时取消路线选择

      // 清理当前地图显示
      this.clearCurrentMapDisplay();

      // 显示所有合同的路线
      this.showAllContractRoutes();

      if (this.infoBox) {
        this.infoBox.close();
      }
    },

    // 显示所有合同路线（去重）
    showAllContractRoutes() {
      const displayedRoutes = new Set();
      let allPathPoints = []; // 收集所有路线的路径点

      Object.values(this.contractSelectRouteMap).forEach(routes => {
        routes.forEach(route => {
          if (!displayedRoutes.has(route.routeId)) {
            displayedRoutes.add(route.routeId);
            // 不设置单独的视口，收集路径点
            const pathPoints = this.showRouteOnMap(route.routeId, "#2979ff");
            allPathPoints.push(...pathPoints);
          }
        });
      });

      // 统一设置视口以显示所有路线
      if (allPathPoints.length > 0) {
        this.map.setViewport(allPathPoints);
      }
    },

    // 在地图上显示指定路线
    showRouteOnMap(routeId, color = "#2979ff") {
      const polylineArr = this.routeMap[routeId];
      if (polylineArr) {
        let pathArr = [];

        // 显示所有相关的 polyline
        polylineArr.forEach(polyline => {
          polyline.show();
          polyline.setStrokeColor(color);

          // 收集路径点用于设置视口
          let path = polyline.getPath();
          pathArr.push(...path);
        });
        return pathArr; // 返回路径点数组，供调用方统一处理视口
      }
      return [];
    },

    // 清理当前地图显示
    clearCurrentMapDisplay() {
      Object.keys(this.routeMap).forEach(routeId => {
        const polylineArr = this.routeMap[routeId];
        if (polylineArr) {
          polylineArr.forEach(polyline => {
            polyline.hide();
            polyline.setStrokeColor("#cccccc");
          });
        }
      });
    },

    // 清理全部线路模式的显示
    clearAllRoutesDisplay() {
      this.clearCurrentMapDisplay();
    },

    // 获取所有路线
    getAllRoute() {
      $http.getAllRouteLine().then(res => {
        if (res.code === 0 && res.data) {
          this.allRouteList = res.data;
          this.allRouteList.map(item => {
            if (item.line) {
              let lineData = JSON.parse(item.line);
              let isMulti = lineData.every(line => Array.isArray(line));
              if (isMulti) {
                lineData.map(line => {
                  this.drawRoute(line, item);
                });
              } else {
                this.drawRoute(lineData, item);
              }
            }
          });
          if (this.isView) {
            this.$nextTick(() => {
              this.toggleShowAllRoutes();
            });
          }
        } else {
          this.$message.error(res.msg || "获取路线列表失败");
        }
      });
    },
    // 绘制路线
    drawRoute(line, data) {
      if (this.map) {
        let points = line.map(point => new BMap.Point(point.lng, point.lat));
        let polyline = new BMap.Polyline(points, {
          strokeColor: "#cccccc",
          strokeWeight: 4,
        });
        polyline.routeId = data.rteLinePk;
        polyline.routeData = data; // 保存路线数据
        polyline.hide();

        if (this.routeMap[data.rteLinePk]) {
          this.routeMap[data.rteLinePk].push(polyline);
        } else {
          this.routeMap[data.rteLinePk] = [polyline];
        }

        polyline.addEventListener("click", e => {
          // 先处理路线选择逻辑
          this.selectRoute(e.target.routeData);

          // 显示当前路线的所有 polyline（处理多维数组情况）
          const routeId = e.target.routeData.rteLinePk;
          const allPolylines = this.routeMap[routeId];
          if (allPolylines) {
            allPolylines.forEach(pl => {
              pl.show();
              pl.setStrokeColor("#ff4081"); // 选中路线颜色
            });
          }

          // 显示路线信息窗口
          this.showRouteInfoBox(data, e.point);
        });
        this.map.addOverlay(polyline);
      }
    },
    // 显示路线信息窗口
    showRouteInfoBox(data, point) {
      // 关闭已有的信息窗口
      if (this.infoBox) {
        this.infoBox.close();
      }
      // 创建信息窗口内容
      let content = `<div class="route-info-box">
        <div class="route-info-title">线路信息</div>
        <div class="route-info-content">
          <p>线路名称: ${data.label || "未命名路线"}</p>
        </div>`;

      // 如果有选择合同，显示添加/删除按钮(右侧列表已右添加和删除按钮)
      if (false && !this.isView && this.selectContractId) {
        const contractId = this.contractSelectRouteMap[this.selectContractId] || [];
        const isSelected = contractId.some(item => item.routeId == data.rteLinePk) || false;

        content += `<div class="route-info-actions">
          <button id="route-action-btn" class="${isSelected ? "remove-btn" : "add-btn"}">
            ${isSelected ? "删除路线" : "添加路线"}
          </button>
        </div>`;
      }

      content += `</div>`;

      // 创建信息窗口
      this.infoBox = new BMapLib.InfoBox(this.map, content, {
        boxStyle: {
          background: "#fff",
          width: "280px",
          padding: "10px",
          borderRadius: "4px",
          boxShadow: "0 2px 6px 0 rgba(0, 0, 0, 0.2)",
        },
        boxClass: "infoBox",
        closeIconMargin: "-6px 5px 0px 0px",
        closeIconUrl: imgsConfig.vec.close_btn,
        enableAutoPan: true,
      });

      this.infoBox.open(point);
      // if (this.time) {
      //   clearTimeout(this.time);
      //   this.time = null;
      // }
      // 添加按钮点击事件
      /* this.time = setTimeout(() => {
        const actionBtn = document.getElementById("route-action-btn");
        if (actionBtn) {
          actionBtn.addEventListener("click", () => {
            const contractRouteList = this.contractSelectRouteMap[this.selectContractId] || [];
            let routeData = contractRouteList.find(item => item.routeId == data.rteLinePk);
            // const isSelected = contractRouteList.some(item => item.routeId == data.rteLinePk) || false;
            if (routeData) {
              this.submitRemoveRoute(routeData);
            } else {
              this.submitAddRoute(data);
            }
            this.infoBox.close();
          });
        }
        clearTimeout(this.time);
        this.time = null;
      }, 100); */
    },
    submitRemoveRoute(routeData) {
      this.$confirm("确定删除此路线吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            id: routeData.id,
            templateId: routeData.templateId,
          };
          $http.delRoute(params).then(res => {
            if (res.code == 0) {
              this.$message.success("删除路线成功");
              let index = this.contractSelectRouteMap[routeData.contractId].findIndex(item => item.routeId == routeData.routeId);
              this.contractSelectRouteMap[routeData.contractId].splice(index, 1);

              // 如果合同没有路线了，清理空的合同数据
              if (this.contractSelectRouteMap[routeData.contractId].length === 0) {
                delete this.contractSelectRouteMap[routeData.contractId];
              }

              this.updateRouteDataList();
              this.resetSelectRoute();
            } else {
              this.$message.error(res.msg || "删除失败");
            }
          });
        })
        .catch(() => {});
    },
    submitAddRoute(data) {
      // 添加操作不需要确认对话框
      let params = {
        routeId: data.rteLinePk,
        templateId: this.templateId,
        contractId: this.selectContractId,
        denyType: "both",
        notWeight: false,
      };

      $http
        .addRoute(params)
        .then(res => {
          if (res.code == 0 && res.data) {
            // this.$message.success("添加路线成功");
            let resData = Object.assign({}, res.data, { routeName: data.label || null });
            if (this.contractSelectRouteMap[params.contractId]) {
              this.contractSelectRouteMap[params.contractId].push(resData);
            } else {
              this.contractSelectRouteMap[params.contractId] = [resData];
            }
            this.updateRouteDataList();
            this.resetSelectRoute();
            this.showDefaultRoute(this.contractSelectRouteMap[res.data.contractId]);
          } else {
            this.$message.error(res.msg || "添加失败");
          }
        })
        .catch(error => {
          console.error("添加路线失败:", error);
          this.$message.error("添加路线失败");
        });
    },

    // 更新合同已选择的线路数据列表（底部表格数据）- 树形结构
    updateRouteDataList() {
      let treeList = [];
      for (const contractId of Object.keys(this.contractSelectRouteMap)) {
        let routeList = this.contractSelectRouteMap[contractId] || [];
        if (routeList.length > 0) {
          // 创建子节点
          const children = routeList.map(route => ({
            ...route,
            contractName: this.getContractName(contractId),
            key: `route_${route.routeId}_${contractId}`, // 确保路线节点也有唯一 id
            isContract: false, // 标识这是路线节点
          }));

          // 创建合同节点
          const contractNode = {
            key: `contract_${contractId}`, // 树形表格需要唯一的 id
            contractId: contractId,
            contractName: this.getContractName(contractId),
            isContract: true, // 标识这是合同节点
            routeCount: routeList.length,
            children: children,
          };

          treeList.push(contractNode);
        }
      }

      if (process.env.NODE_ENV === "development") {
        console.log("更新树形表格数据:", {
          contractCount: treeList.length,
          totalRoutes: treeList.reduce((sum, contract) => sum + contract.routeCount, 0),
          treeData: treeList,
        });
      }

      this.$set(this, "routeDataList", treeList);
    },

    // 查看合同操作
    viewContract(contractNode) {
      // 设置当前查看的合同信息
      this.currentViewContract = {
        contractId: contractNode.contractId,
        contractName: contractNode.contractName,
      };

      // 获取该合同的路线列表
      const routeList = this.contractSelectRouteMap[contractNode.contractId] || [];
      this.currentViewRoutes = [...routeList];

      if (process.env.NODE_ENV === "development") {
        console.log("合同路线列表:", {
          contractId: contractNode.contractId,
          contractName: contractNode.contractName,
          routeCount: routeList.length,
          routes: routeList,
        });
      }

      // 打开 dialog
      this.innerVisible = true;
    },

    // 打开企业查询弹窗
    openEnterpriseQuery(contract) {
      this.currentQueryContract = contract;
      this.enterpriseOptions = [];
      this.selectedEnterprises = [];
      this.previewRoutes = [];
      this.searchLoading = false;
      // this.activeCollapse = ["routePreview"]; // 默认展开路线预览
      this.enterpriseQueryVisible = true;
    },

    // 远程搜索企业（带防抖）
    remoteSearchEnterprises(query) {
      if (!query || !query.trim() || query.length < 2) {
        this.enterpriseOptions = [];
        return;
      }

      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置防抖，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.doSearchEnterprises(query.trim());
      }, 500);
    },

    // 执行企业搜索
    doSearchEnterprises(query) {
      this.searchLoading = true;
      const params = {
        tagName: query,
      };

      $http
        .getDefaultRoute(params)
        .then(res => {
          this.searchLoading = false;
          if (res.code === 0 && res.data && res.data.length > 0) {
            // 处理企业数据，将 tagName 按逗号分隔，并过滤与查询关键词相关的企业简称
            this.enterpriseOptions = res.data
              .map(item => {
                const enterprises = item.tagName
                  .split(",")
                  .filter(name => name.trim())
                  .filter(name => name.trim().includes(query)); // 只保留包含查询关键词的企业简称
                return enterprises.map(name => ({
                  id: `${item.id}_${name.trim()}`, // 生成唯一ID
                  routeId: item.routeId,
                  tagName: name.trim(),
                  originalData: item,
                }));
              })
              .flat();
          } else {
            this.enterpriseOptions = [];
          }
        })
        .catch(error => {
          this.searchLoading = false;
          this.enterpriseOptions = [];
          console.error("搜索企业失败:", error);
          this.$message.error("搜索企业失败");
        });
    },

    // 处理企业选择变化，生成路线预览
    generateRoutePreview(selectedValues) {
      // 使用组件的 selectedEnterprises 数据，确保数据一致性
      const currentSelected = selectedValues || this.selectedEnterprises;

      if (!currentSelected || currentSelected.length === 0) {
        this.previewRoutes = [];
        return;
      }

      // 收集所有选中企业的 routeId
      const allRouteIds = [];      
      currentSelected.forEach(enterprise => {
        const routeIds = enterprise.originalData.routeId.split(",").filter(id => id.trim());
          allRouteIds.push(...routeIds.map(id => id.trim()));
      });

      // 去重
      const uniqueRouteIds = [...new Set(allRouteIds)];

      // 根据路线ID获取路线名称
      this.previewRoutes = uniqueRouteIds
        .map(routeId => {
          const route = this.allRouteList.find(r => r.rteLinePk == routeId);
          return {
            routeId: routeId,
            routeName: route ? route.label : "未找到路线",
            routeData: route,
          };
        })
        .filter(route => route.routeData); // 过滤掉未找到的路线

      if (process.env.NODE_ENV === "development") {
        console.log("预览路线:", this.previewRoutes);
      }
    },

    // 批量添加路线
    batchAddRoutes() {
      if (this.previewRoutes.length === 0) {
        this.$message.warning("没有可添加的路线");
        return;
      }

      this.listLoading = true;
      let addedCount = 0;
      const totalCount = this.previewRoutes.length;

      if (process.env.NODE_ENV === "development") {
        console.log("开始批量添加路线:", {
          contractId: this.currentQueryContract.id,
          routes: this.previewRoutes,
        });
      }

      // 循环添加每条路线
      this.previewRoutes.forEach(route => {
        const params = {
          routeId: route.routeId,
          templateId: this.templateId,
          contractId: this.currentQueryContract.id,
          denyType: "both",
          notWeight: false,
        };

        $http
          .addRoute(params)
          .then(res => {
            if (res.code == 0 && res.data) {
              addedCount++;
              let resData = Object.assign({}, res.data, { routeName: route.routeName });

              if (this.contractSelectRouteMap[params.contractId]) {
                this.contractSelectRouteMap[params.contractId].push(resData);
              } else {
                this.contractSelectRouteMap[params.contractId] = [resData];
              }

              // 如果是最后一个路线，完成添加
              if (addedCount === totalCount) {
                this.listLoading = false;
                this.updateRouteDataList();
                this.$message.success(`成功添加 ${addedCount} 条路线`);

                // 关闭弹窗
                this.enterpriseQueryVisible = false;

                // 如果当前选中了该合同，显示路线
                if (this.selectContractId === this.currentQueryContract.id) {
                  this.showDefaultRoute(this.contractSelectRouteMap[this.currentQueryContract.id]);
                }
              }
            } else {
              this.listLoading = false;
              this.$message.error(`添加路线失败: ${res.msg || "未知错误"}`);
            }
          })
          .catch(error => {
            this.listLoading = false;
            console.error("添加路线失败:", error);
            this.$message.error("添加路线失败");
          });
      });
    },

    // 处理线路列表项点击
    handleRouteItemClick(route) {
      // 选择新路线
      this.selectRoute(route);
      // 在地图中显示该路线
      this.addSelectedRoute(route.rteLinePk);

      // 调试信息：显示该路线包含的 polyline 数量
      if (process.env.NODE_ENV === "development") {
        const polylineArr = this.routeMap[route.rteLinePk];
        console.log(`路线 "${route.label}" 包含 ${polylineArr ? polylineArr.length : 0} 条 polyline`);
      }
    },

    // 判断路线是否在当前选中合同中
    isRouteInCurrentContract(routeId) {
      if (this.selectContractId) {
        const routes = this.contractSelectRouteMap[this.selectContractId] || [];
        return routes.some(route => String(route.routeId) === String(routeId));
      }
      return false;
    },

    // 获取路线在当前合同中的数据
    getRouteDataInCurrentContract(routeId) {
      if (this.selectContractId) {
        const routes = this.contractSelectRouteMap[this.selectContractId] || [];
        return routes.find(route => String(route.routeId) === String(routeId));
      }
      return null;
    },

    // 确认删除路线（带确认对话框）
    confirmRemoveRoute(route) {
      const routeData = this.getRouteDataInCurrentContract(route.rteLinePk);
      if (routeData) {
        this.submitRemoveRoute(routeData);
      }
    },
    // 重置
    reset() {
      this.$set(this, "selectContractId", ""); // 重置合同ID
      this.$set(this, "showAllRoutes", false); // 重置显示所有路线
      this.clearCurrentMapDisplay(); // 清除地图显示
      this.resetSelectRoute(); // 重置选择路线
    },
    // 重置选择路线
    resetSelectRoute() {
      this.removeSelectedRoute(this.selectedRouteId); // 在地图上取消已选中的路线
      this.$set(this, "selectedRouteId", "");
      if (this.infoBox) {
        this.infoBox.close();
      }
    },

    // 判断路线是否被选中
    isRouteSelected(routeId) {
      return this.selectedRouteId == routeId;
    },

    // 获取合同名称
    getContractName(id) {
      let index = this.contractList.findIndex(item => item.id == id);
      return "合同-" + (index + 1);
    },

    // 判断是否已选择合同（item=合同数据）
    isSelect(item) {
      if (this.selectContractId === item.id) {
        return "select";
      }
      return "";
    },
    // 设置合同已选择路线
    setData(data) {
      let routeList = data.routes;
      this.$set(this, "contractSelectRouteMap", {});
      routeList.map(item => {
        if (this.contractSelectRouteMap[item.contractId]) {
          this.contractSelectRouteMap[item.contractId].push(item);
        } else {
          this.contractSelectRouteMap[item.contractId] = [item];
        }
      });
      this.updateRouteDataList();
    },
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },
};
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 0px;
  &::v-deep .el-card__header {
    padding: 15px 20px;
  }
  .header-box {
    height: 25px;
    padding-left: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      width: 3px;
      height: 100%;
      background: #409eff;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.cards-container {
  position: relative;
  .contract-list-box {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 120px;
    max-height: calc(100% - 40px);
    overflow-y: auto;
    z-index: 10;
    box-sizing: border-box;

    .all-routes-btn {
      width: 120px;
      height: 40px;
      line-height: 40px;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      margin-bottom: 15px;
      padding-left: 15px;
      cursor: pointer;
      text-align: center;
      padding-left: 0;

      // &:hover {
      //   background-color: rgba(64, 158, 255, 0.1);
      // }

      &.active {
        background-color: #409eff;
        color: #fff;
      }
    }

    .contract-item {
      width: 120px;
      height: 40px;
      line-height: 40px;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      font-size: 16px;
      color: #333;
      margin-bottom: 15px;
      padding-left: 15px;
      cursor: pointer;
      &.select {
        background-color: #409eff;
        color: #fff;
      }
    }
  }

  .route-list-box {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 240px;
    height: calc(100% - 40px);
    z-index: 10;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .search-box {
      margin-bottom: 10px;
      width: 100%;
    }

    .route-list {
      flex: 1;
      overflow-y: auto;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 4px;
      padding: 10px;
      box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.05);

      .route-item {
        padding: 10px;
        margin-bottom: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: #e0e0e0;
        }

        &.selected {
          background-color: #ff4081;
          color: #fff;
        }

        .route-content {
          display: flex;
          align-items: center;
          // flex-direction: column;
          // gap: 8px;
        }

        .route-name {
          font-size: 14px;
          font-weight: 500;
          // width: 90%;
          flex: 1;
        }

        .route-actions {
          display: flex;
          justify-content: flex-end;
          margin-left: 10px;
          .el-button {
            padding: 4px 8px;
            font-size: 12px;
            min-width: 50px;
          }
        }
      }
    }
  }
  &::v-deep .infoBox {
    img {
      width: 35px;
    }
  }
}

// 文本溢出省略号
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 全局样式，需要添加到页面中
:global {
  .route-info-box {
    font-family: Arial, sans-serif;

    .route-info-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
    }

    .route-info-content {
      margin-bottom: 10px;

      p {
        margin: 5px 0;
        font-size: 14px;
      }
    }

    .route-info-actions {
      text-align: right;

      button {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &.add-btn {
          background-color: #409eff;
          color: white;
        }

        &.remove-btn {
          background-color: #f56c6c;
          color: white;
        }
      }
    }
  }
}

// Dialog 中的路线标签容器样式
.route-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 0 20px 20px;
  max-height: 70vh;
  overflow-y: auto;

  .route-tag {
    margin: 0; // 移除默认的 margin
    white-space: nowrap; // 防止标签内文字换行
    max-width: 100%; // 防止超出容器
    overflow: hidden; // 超长内容隐藏
    text-overflow: ellipsis; // 超长显示省略号
  }
}

// 企业查询弹窗样式
.enterprise-query-dialog {
  .dialog-content {
    padding: 0 20px;
  }

  .query-section {
    margin-bottom: 20px;

    .main-title {
      margin-bottom: 15px;
      color: #212121;
      font-size: 16px;
    }

    .sub-title {
      margin-bottom: 15px;
      color: #909399;
      font-size: 14px;
    }

    .enterprise-select {
      width: 30%;
      margin-bottom: 15px;
    }
  }

  .route-preview-section {
    margin-top: 20px;

    .preview-description {
      margin-bottom: 15px;
      color: #606266;
    }

    .no-routes-tip {
      color: #909399;
      text-align: center;
      padding: 20px;
    }
  }
}
</style>

<style>
/* 全局样式 */
.route-info-box {
  font-family: Arial, sans-serif;
}

.route-info-box .route-info-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.route-info-box .route-info-content {
  margin-bottom: 10px;
}

.route-info-box .route-info-content p {
  margin: 5px 0;
  font-size: 14px;
}

.route-info-box .route-info-actions {
  text-align: right;
}

.route-info-box .route-info-actions button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.route-info-box .route-info-actions button.add-btn {
  background-color: #409eff;
  color: white;
}

.route-info-box .route-info-actions button.remove-btn {
  background-color: #f56c6c;
  color: white;
}
</style>
