<template>
  <el-dialog class="road-pass-info" title="通行证详情" :close-on-click-modal="false" :visible.sync="visible" width="100%" top="1vh">
    <div style="padding: 5px 20px;" v-if="visible" v-loading="formLoading">
      <el-row :gutter="20">
        <el-col :span="11">
          <div class="left-box">
            <TemplateFrom :isView="isView" ref="templateFrom"></TemplateFrom>
            <ContractFrom :isView="isView" ref="contractFrom" :templateId="templateId" @update="updateContractList"></ContractFrom>
            <VehicleFrom :isView="isView" ref="vehicleFrom" :templateId="templateId"></VehicleFrom>
            <submitFrom :isView="isView" ref="submitFrom" :templateId="templateId" @changeLoading="changeLoading"></submitFrom>
          </div>
          <div style="height: 50px;float: right;padding-right: 20px; line-height: 50px;">
            <el-button type="primary" @click="visible = false">关闭</el-button>
          </div>
        </el-col>
        <el-col :span="13">
          <RouteMapFrom :isView="isView" v-if="visible" ref="routeMapFrom" :contractList="newContractList" :templateId="templateId"></RouteMapFrom>
        </el-col>
      </el-row>
    </div>
  </el-dialog>
</template>

<script>
import * as $http from "@/api/roadPass";
import TemplateFrom from './roadPassForm/template-from.vue';
import ContractFrom from './roadPassForm/contract-from.vue';
import RouteMapFrom from './roadPassForm/route-map-from.vue';
import VehicleFrom from './roadPassForm/vehicle-from';
import submitFrom from './roadPassForm/submit-from.vue';
import { cloneDeep } from "lodash";
export default {
  name: "",
  components: {
    TemplateFrom,
    ContractFrom,
    VehicleFrom,
    RouteMapFrom,
    submitFrom,
  },
  data() {
    return {
      visible: false,
      formLoading: false,
      isView:true,
      templateId: "",
      templateData: {},
      newContractList: [],
      
    };
  },
  methods: {
    init(id) {
      this.visible = true;
      this.templateId = id;
      if(id){
        $http.getTemplateDetail(id).then((res) => {
          if (res.code == 0 && res.data) {
            this.setTemplateData(res.data);
          } else {
            this.$message.error(res.msg || "获取模板详情失败");
          }
        });
      }
    },
    setTemplateData(data){
      this.$set(this, "templateData", data);
      let contracts = cloneDeep(data.contracts);
      this.newContractList = contracts || [];
      this.$nextTick(() => {
        this.$refs.templateFrom?.setData(data);
        this.$refs.contractFrom?.setData(data);
        this.$refs.vehicleFrom?.setData(data);
        this.$refs.routeMapFrom?.setData(data);
        this.$refs.submitFrom?.setData(data);
      });
    },
    updateContractList(contractList) {
      let data = cloneDeep(contractList);
      this.newContractList = data;
      // this.$set(this,"newContractList", data);
    },
    changeLoading(loading, visible = false) {
      this.formLoading = loading;
      if (visible) {
        this.visible = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.road-pass-info{
  &::v-deep .el-dialog{
    margin-bottom: 0px;
    .el-dialog__body{
      height: 92vh;
      overflow-y: auto;
    }
  }
  .el-dialog__body{
    padding: 0 20px 5px;
    .left-box{
      height: 84vh;
      overflow-y: auto;
    }
  }
}
</style>