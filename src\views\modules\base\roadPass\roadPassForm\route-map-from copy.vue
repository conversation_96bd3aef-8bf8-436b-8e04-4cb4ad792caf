<template>
  <el-card class="road-pass-card">
    <div slot="header" class="header-box">
      <div>通行证线路规划</div>
    </div>
    <div class="cards-container">
      <div style="position: relative">
        <BMapComp :config="mapConfig" @mapReady="mapReady"></BMapComp>
        <div class="contract-list-box">
          <div class="contract-item" :class="isSelect(item)" v-for="(item, index) in contractList" :key="item.id" @click="selectContract(item)">
            <span>合同-{{ index + 1 }}</span>
          </div>
        </div>
        <div class="route-list-box">
          <div class="search-box">
            <el-input placeholder="搜索路线" v-model="searchText" clearable prefix-icon="el-icon-search" @input="searchRoute"></el-input>
          </div>
          <div class="route-list">
            <div class="route-item" :class="{ selected: isRouteSelected(item.rteLinePk) }" v-for="item in filteredRouteList" :key="item.rteLinePk" @click="changeSelectRoute(item)">
              <div class="route-name text-ellipsis" :title="item.label">{{ item.label || "未命名路线" }}</div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <el-table class="el-table" :data="routeDataList" border style="width: 100%" v-loading="listLoading" height="275" size="small">
          <el-table-column label="合同ID" prop="contractId" min-width="140">
            <template slot-scope="scope">
              {{ getContractName(scope.row.contractId) }}
            </template>
          </el-table-column>
          <el-table-column label="路线名称" prop="routeName" min-width="140"></el-table-column>
          <el-table-column label="时段禁行" prop="denyType" min-width="140">
            <template slot-scope="scope">
              <el-select size="small" disabled v-model="scope.row.denyType" placeholder="请选择">
                <el-option v-for="item in denyTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="限制通行" prop="notWeight" min-width="140">
            <template slot-scope="scope">
              <el-select size="small" disabled v-model="scope.row.notWeight" placeholder="请选择">
                <el-option v-for="item in notWeightOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column v-if="!isView" label="操作" min-width="100" align="center">
            <template slot-scope="scope">
              <el-button type="text" title="删除" @click="submitRemoveRoute(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-card>
</template>

<script>
import BMapComp from "./BMapComp";
import * as $http from "@/api/roadPass";
import imgsConfig from "static/jsonConfig/mapMonitorImagesConfig.json";
export default {
  name: "",
  components: {
    BMapComp,
  },
  props: {
    contractList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    templateId: {
      type: String,
      default: false, // 默认
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      map: null,
      mapConfig: {
        scrollWheelZoom: true,
        mapHeight: 400,
        centerAndZoom: {
          point: [121.681275, 29.973008],
          zoom: 13,
          city: "宁波市",
        },
      },

      allRouteList: [],
      filteredRouteList: [], // 过滤后的路线列表
      searchText: "", // 搜索文本
      selectedRouteIds: [], // 选中的路线ID

      time:null,

      selectContractId: [],
      // routeMap:new Map(), // 存储路线数据
      routeMap: {}, // 存储路线数据
      selectedRouteId: "", // 当前选中的路线ID
      contractRouteMap: {}, // 存储合同默认的路线ID
      contractSelectRouteMap: {}, // 存储合同列已添加的路线ID
      infoBox: null, // 信息窗口

      listLoading:false,
      routeDataList:[],
      // morning 早高峰禁行,  evening 晚高峰禁行, both 高峰禁行, none 全时段通行， 默认写both
      denyTypeOptions: [
        {label:"高峰禁行", value:"both"},
        {label:"早高峰禁行", value:"morning"},
        {label:"晚高峰禁行", value:"evening"},
        {label:"全时段通行", value:"none"},
      ],
      notWeightOptions: [
        {label:"不限空重车", value:false},
        {label:"仅空车", value:true},
      ],
    };
  },
  watch: {
    templateId: {
      handler(newVal) {
        if (newVal) {
          this.reset();
        }
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    // 选择|取消选择合同
    selectContract(data) {
      let idnex = this.selectContractId.findIndex(item => item == data.id);
      if (idnex == -1) {
        this.selectContractId.push(data.id);
        this.showDefaultRoute(this.contractSelectRouteMap[data.id]);
      } else {
        this.selectContractId.splice(idnex, 1);
        this.resetRouteColor(data);
      }
      if (this.infoBox) {
        this.infoBox.close();
      }
    },
    // 显示合同已添加路线
    showDefaultRoute(data) {
      if (data && data.length > 0) {
        data.map(item => {
          let polylineArr = this.routeMap[item.routeId];
          if (polylineArr) {
            polylineArr.map(polyline => {
              polyline.show();
              polyline.setStrokeColor("#2979ff");
            });
          }
        });
      }
    },
    // 重置合同已添加路线
    resetRouteColor(data) {
      // 使用保存的路线ID直接重置颜色，避免API调用
      const routeIds = this.contractSelectRouteMap[data.id] || [];
      routeIds.map(item => {
        let polylineArr = this.routeMap[item.routeId];
        if (polylineArr) {
          polylineArr.map(polyline => {
            polyline.hide();
            polyline.setStrokeColor("#adadad");
          });
        }
      });
      // 清除保存的路线ID
      // delete this.contractRouteMap[data.id];
    },
    // 在路线列表中切换选择路线
    changeSelectRoute(route) {
      this.selectRoute(route);
      this.addSelectedRoute(route.rteLinePk);
    },
    // 选择路线
    selectRoute(route) {
      // 取消先前选择的路线
      this.removeSelectedRoute(this.selectedRouteId);
      const routeId = route.rteLinePk;
      this.selectedRouteId = routeId;
      // this.addSelectedRoute(routeId);
    },
    // 添加选中路线
    addSelectedRoute(routeId) {
      const polylineArr = this.routeMap[routeId];
      if (polylineArr) {
        let pathArr = [];
        polylineArr.map(polyline => {
          // polyline.setStrokeColor("#ff4081"); // 选中路线颜色
          let path = polyline.getPath();
          let index = Math.floor(path.length / 2);
          polyline.dispatchEvent("click", {
            type: "click",
            target: polyline,
            point: path[index], // 使用折线的中间点
          });
          pathArr.push(...path);
        });
        if (pathArr.length) {
          // this.map.setViewport(pathArr);
        }
      }
    },
    // 移除选中路线
    removeSelectedRoute(routeId) {
      if (routeId) {
        // 重置路线颜色
        const polylineArr = this.routeMap[routeId];
        if (polylineArr) {
          polylineArr.forEach(polyline => {
            if (!this.isView) {
              polyline.hide();
              polyline.setStrokeColor("#adadad"); // 默认路线颜色
            } else {
              polyline.setStrokeColor("#2979ff"); // 默认路线颜色
            }
          });
        }
      }
    },
    // 地图加载完成
    mapReady(map) {
      this.map = map;
      this.getAllRoute();
    },
    // 获取所有路线
    getAllRoute() {
      $http.getAllRouteLine().then(res => {
        if (res.code === 0 && res.data) {
          this.allRouteList = res.data;
          this.filteredRouteList = [...this.allRouteList]; // 初始化过滤列表
          this.allRouteList.map(item => {
            if (item.line) {
              let lineData = JSON.parse(item.line);
              let isMulti = lineData.every(line => Array.isArray(line));
              if (isMulti) {
                lineData.map(line => {
                  this.drawRoute(line, item);
                });
              } else {
                this.drawRoute(lineData, item);
              }
            }
          });
          if (this.isView) {
            this.contractList.map(item => {
              this.selectContract(item);
            });
          }
        } else {
          this.$message.error(res.msg || "获取路线列表失败");
        }
      });
    },
    // 绘制路线
    drawRoute(line, data) {
      if (this.map) {
        let points = line.map(point => new BMap.Point(point.lng, point.lat));
        let polyline = new BMap.Polyline(points, {
          strokeColor: "#adadad",
          strokeWeight: 4,
        });
        polyline.routeId = data.rteLinePk;
        polyline.routeData = data; // 保存路线数据
        polyline.hide();

        if (this.routeMap[data.rteLinePk]) {
          this.routeMap[data.rteLinePk].push(polyline);
        } else {
          this.routeMap[data.rteLinePk] = [polyline];
        }

        polyline.addEventListener("click", e => {
          this.selectRoute(e.target.routeData);
          // 选择路线
          e.target.show();
          e.target.setStrokeColor("#ff4081");
          // 显示路线信息窗口
          this.showRouteInfoBox(data, e.point);
        });
        this.map.addOverlay(polyline);
      }
    },
    // 显示路线信息窗口
    showRouteInfoBox(data, point) {
      // 关闭已有的信息窗口
      if (this.infoBox) {
        this.infoBox.close();
      }

      // 创建信息窗口内容
      let content = `<div class="route-info-box">
        <div class="route-info-title">${data.label || "未命名路线"}</div>
        <div class="route-info-content">
          <p>路线ID: ${data.rteLinePk}</p>
        </div>`;

      // 如果有选择合同并且只选择了一份合同，显示添加/删除按钮
      if (!this.isView && this.selectContractId.length == 1) {
        const contractId = this.contractSelectRouteMap[this.selectContractId[0]] || [];
        const isSelected = contractId.some(item => item.routeId == data.rteLinePk) || false;

        content += `<div class="route-info-actions">
          <button id="route-action-btn" class="${isSelected ? "remove-btn" : "add-btn"}">
            ${isSelected ? "删除路线" : "添加路线"}
          </button>
        </div>`;
      }

      content += `</div>`;

      // 创建信息窗口
      this.infoBox = new BMapLib.InfoBox(this.map, content, {
        boxStyle: {
          background: "#fff",
          width: "280px",
          padding: "10px",
          borderRadius: "4px",
          boxShadow: "0 2px 6px 0 rgba(0, 0, 0, 0.2)",
        },
        boxClass: "infoBox",
        closeIconMargin: "-6px 5px 0px 0px",
        closeIconUrl: imgsConfig.vec.close_btn,
        enableAutoPan: true,
      });

      this.infoBox.open(point);
      if(this.time){
        clearTimeout(this.time);
        this.time = null;
      }
      // 添加按钮点击事件
      this.time = setTimeout(() => {
        const actionBtn = document.getElementById("route-action-btn");
        if (actionBtn) {
          actionBtn.addEventListener("click", () => {
            const contractRouteList = this.contractSelectRouteMap[this.selectContractId[0]] || [];
            let routeData = contractRouteList.find(item => item.routeId == data.rteLinePk);
            // const isSelected = contractRouteList.some(item => item.routeId == data.rteLinePk) || false;
            if (routeData) {
              this.submitRemoveRoute(routeData);
            } else {
              this.submitAddRoute(data);
            }
            this.infoBox.close();
          });
        }
        clearTimeout(this.time);
        this.time = null;
      }, 100);
    },
    submitRemoveRoute(routeData) {
      this.$confirm("确定删除此路线吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            id: routeData.id,
            templateId: routeData.templateId,
          };
          $http.delRoute(params).then(res => {
            if (res.code == 0) {
              this.$message.success("删除路线成功");
              let index = this.contractSelectRouteMap[routeData.contractId].findIndex(item => item.routeId == routeData.routeId);
              this.contractSelectRouteMap[routeData.contractId].splice(index, 1);
              this.updateRouteDataList();
              this.resetSelectRoute();
            } else {
              this.$message.error(res.msg || "删除失败");
            }
          });
        })
        .catch(() => {});
    },
    submitAddRoute(data) {
      this.$confirm("确定添加此路线吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            routeId: data.rteLinePk,
            templateId: this.templateId,
            contractId: this.selectContractId[0],
            denyType: "both",
            notWeight: false,
          };
          $http.addRoute(params).then(res => {
            if (res.code == 0 && res.data) {
              this.$message.success("添加路线成功");
              let resData = Object.assign({},res.data, { routeName: data.label || null });
              if (this.contractSelectRouteMap[params.contractId]) {
                this.contractSelectRouteMap[params.contractId].push(resData);
              } else {
                this.contractSelectRouteMap[params.contractId] = [resData];
              }
              this.updateRouteDataList()
              this.resetSelectRoute();
              this.showDefaultRoute(this.contractSelectRouteMap[res.data.contractId]);
            } else {
              this.$message.error(res.msg || "删除失败");
            }
          });
        })
        .catch(() => {});
    },
    updateRouteDataList(){
      let list = []
      for (const key of Object.keys(this.contractSelectRouteMap)) {
        let routeList = this.contractSelectRouteMap[key] || [];
        list.push(...routeList);
      }      
      this.$set(this,'routeDataList',list)
    },
    // 搜索路线
    searchRoute() {
      if (!this.searchText) {
        this.filteredRouteList = [...this.allRouteList];
        return;
      }
      this.filteredRouteList = this.allRouteList.filter(item => {
        return item.label && item.label.toLowerCase().includes(this.searchText.toLowerCase());
      });
    },
    reset() {
      this.$set(this, "selectContractId", []);
      this.removeSelectedRoute(this.selectedRouteId);
      this.$set(this, "selectedRouteId", "");
      if (this.infoBox) {
        this.infoBox.close();
      }
    },
    resetSelectRoute() {
      this.removeSelectedRoute(this.selectedRouteId);
      this.$set(this, "selectedRouteId", "");
      if (this.infoBox) {
        this.infoBox.close();
      }
    },

    // 判断路线是否被选中
    isRouteSelected(routeId) {
      return this.selectedRouteId == routeId;
    },

    getDefaultRoute(data) {
      let params = {
        load: data.loadAddress,
        unload: data.unloadAddress,
      };
      $http.getDefaultRoute(params).then(res => {
        if (res.code === 0 && res.data) {
          // 保存合同对应的路线ID
          this.contractRouteMap[data.id] = res.data.map(item => item.routeId);
          this.showDefaultRoute(res.data);
        } else {
          this.$message.error(res.msg || "获取默认路线失败");
        }
      });
    },

    getContractName(id){
      console.log("getContractName",this.contractList);
      
      let index = this.contractList.findIndex((item) => item.id == id);
      return '合同-' + (index + 1);
    },

    isSelect(item) {
      let isSelect = this.selectContractId.includes(item.id);
      if (isSelect) {
        return "select";
      }
      return "";
    },
    // 设置合同已选择路线
    setData(data) {
      let routeList = data.routes;
      this.$set(this, "contractSelectRouteMap", {});
      routeList.map(item => {
        if (this.contractSelectRouteMap[item.contractId]) {
          this.contractSelectRouteMap[item.contractId].push(item);
        } else {
          this.contractSelectRouteMap[item.contractId] = [item];
        }
      });
      this.updateRouteDataList();
    },
  },
};
</script>

<style lang="scss" scoped>
.road-pass-card {
  margin-bottom: 0px;
  &::v-deep .el-card__header {
    padding: 15px 20px;
  }
  .header-box {
    height: 25px;
    padding-left: 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      width: 3px;
      height: 100%;
      background: #409eff;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.cards-container {
  position: relative;
  .contract-list-box {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 120px;
    max-height: calc(100% - 40px);
    overflow-y: auto;
    z-index: 10;
    box-sizing: border-box;
    .contract-item {
      width: 120px;
      height: 40px;
      line-height: 40px;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      font-size: 16px;
      color: #333;
      margin-bottom: 15px;
      padding-left: 15px;
      cursor: pointer;
      &.select {
        background-color: #409eff;
        color: #fff;
      }
    }
  }

  .route-list-box {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 200px;
    height: calc(100% - 40px);
    z-index: 10;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .search-box {
      margin-bottom: 10px;
      width: 100%;
    }

    .route-list {
      flex: 1;
      overflow-y: auto;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 4px;
      padding: 10px;
      box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.05);

      .route-item {
        padding: 10px;
        margin-bottom: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: #e0e0e0;
        }

        &.selected {
          background-color: #ff4081;
          color: #fff;
        }

        .route-name {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }
  &::v-deep .infoBox {
    img {
      width: 35px;
    }
  }
}

// 文本溢出省略号
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 全局样式，需要添加到页面中
:global {
  .route-info-box {
    font-family: Arial, sans-serif;

    .route-info-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
    }

    .route-info-content {
      margin-bottom: 10px;

      p {
        margin: 5px 0;
        font-size: 14px;
      }
    }

    .route-info-actions {
      text-align: right;

      button {
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &.add-btn {
          background-color: #409eff;
          color: white;
        }

        &.remove-btn {
          background-color: #f56c6c;
          color: white;
        }
      }
    }
  }
}
</style>

<style>
/* 全局样式 */
.route-info-box {
  font-family: Arial, sans-serif;
}

.route-info-box .route-info-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.route-info-box .route-info-content {
  margin-bottom: 10px;
}

.route-info-box .route-info-content p {
  margin: 5px 0;
  font-size: 14px;
}

.route-info-box .route-info-actions {
  text-align: right;
}

.route-info-box .route-info-actions button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.route-info-box .route-info-actions button.add-btn {
  background-color: #409eff;
  color: white;
}

.route-info-box .route-info-actions button.remove-btn {
  background-color: #f56c6c;
  color: white;
}
</style>
